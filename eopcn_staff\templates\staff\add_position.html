{% extends 'base.html' %}

{% block content %}
{% load static %}

<div class="form-container">
    <h2 class="subheading">Add New Position</h2>
    
    <!-- Page Purpose and Instructions -->
    <div class="info-section">
        <h3>About This Form</h3>
        <p>This section creates a new position number within the EOPCN organization - defining a budgeted position that can be assigned to staff members.</p>
        
        <div class="important-notes">
            <h4>Important Guidelines:</h4>
            <ul>
                <li><strong>Position Numbers:</strong> Each position number should be unique and correspond to approved budget allocations.</li>
                <li><strong>Budget Approval:</strong> Ensure the position has been approved and budgeted for by ELT before creation.</li>
                <li><strong>Position Availability & Status:</strong> The start and end dates determine when a position is available for staff assignment:
                    <ul>
                        <li><em>Available:</em> If today's date falls between the position's start and end date (or no end date), the position status will show as "Available" and can be assigned to staff immediately.</li>
                        <li><em>Future Available:</em> If the start date is in the future, the position status will show as "Future Available" and can only be assigned to staff as of that start date.</li>
                        <li><em>Decommissioned:</em> If the end date is before today's date, the position status will automatically update to "Decommissioned" - it can no longer be used for new assignments but is preserved for historical records.</li>
                        <li><em>Position Status Column:</em> All these availability statuses are displayed in the "Position Status" column on the main position list table for easy reference. I</li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>

    <form id="addPositionForm" method="post">
        {% csrf_token %}

        <!-- Display Errors for Position Form -->
        {% if form.errors %}
            <div class="alert alert-danger">
                <ul>
                {% for field, errors in form.errors.items %}
                    <li>{{ field }}: {{ errors|join:", " }}</li>
                {% endfor %}
                </ul>
            </div>
        {% endif %}
        
        <div class="form-container">
            <!-- Position Details Section -->
            <h3>Position Details</h3>
            <hr>

            {% for field in form %}
                <div class="form-group">
                    {{ field.label_tag }}
                    {{ field }}
                </div>
                {% if field.help_text %}
                    <div class="helper-text">
                        <small class="form-text text-muted">ℹ️ {{ field.help_text }}</small>
                    </div>
                {% endif %}
            {% endfor %}
        </div>

        <div class="form-container">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <button type="submit" class="btn btn-primary mt-3">Save Position</button>
                <a href="{% url 'position_list' %}" class="btn-secondary mt-3">Back to Position List</a>
            </div>
        </div>
    </form>

<style>
    .form-container {
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
        border: 1px solid #ccc;
        border-radius: 8px;
        background-color: #f9f9f9;
        margin-bottom: 15px;
    }

    .button-container {
        flex: 1;
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
        border-radius: 8px;
        margin-top: 0px;
        margin-bottom: 20px;
    }

    .save-container {
        display: flex;
        justify-content: center;
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
        border: 1px solid #ccc;
        border-radius: 8px;
        background-color: #f9f9f9;
        margin-bottom: 15px;
    }

    .form-group {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
    }

    .form-group label {
        width: 200px;
        margin-right: 10px;
        text-align: left;
    }

    .form-group input, 
    .form-group select, 
    .form-group textarea {
        flex: 1;
        padding: 5px;
        max-width: 100%;
    }

    button {
        width: auto;
        padding: 10px 20px;
    }

    a.btn-secondary {
        margin-left: 10px;
        padding: 10px 20px;
        text-decoration: none;
        background-color: #6c757d;
        color: white;
        border-radius: 4px;
    }

    hr {
        margin: 10px 0;
        border: 0;
        border-top: 1px solid #ccc;
    }

    .subheading {
        text-align: center;
    }

    .info-section {
        background-color: #e8f4fd;
        border: 1px solid #b8daff;
        border-radius: 6px;
        padding: 15px;
        margin-bottom: 20px;
    }

    .info-section h3 {
        color: #004085;
        margin-bottom: 10px;
    }

    .important-notes {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 4px;
        padding: 12px;
        margin-top: 15px;
    }

    .important-notes h4 {
        color: #856404;
        margin-bottom: 8px;
    }

    .important-notes ul {
        margin-bottom: 0;
        padding-left: 20px;
    }

    .important-notes li {
        margin-bottom: 5px;
    }

    .form-text {
        font-size: 0.875em;
        color: #6c757d;
        margin-left: 10px;
    }

    .helper-text {
        margin-left: 210px;
        margin-top: -10px;
        margin-bottom: 15px;
    }

    .alert-danger {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
        padding: 10px;
        border-radius: 4px;
        margin-bottom: 15px;
    }

    .alert-danger ul {
        margin: 0;
        padding-left: 20px;
    }
</style>

{% endblock %}
